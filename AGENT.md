## AGENT.md — Augment Agent Guide for Finanze.Pro Emails Service

### Purpose

- This document defines how Augment Agent should interact with this repository: what the project is, how to run it, and the rules for safe, high‑quality assistance.

### Project at a glance

- Name: Finanze.Pro Emails Service
- Type: HTTP microservice for email-related functionality
- Runtime: Bun (JavaScript/TypeScript)
- Framework/Libraries: TypeScript, Express 5, Pino/Pino HTTP, Zod, dotenv + dotenv-expand
- Email: React Email components, Nodemailer, multi-provider email service
- Auth: Static API key for protected endpoints

### Repository layout (high level)

- src/
  - index.ts — App bootstrap (Express, logging, routes)
  - controllers/v1 — Versioned controllers (e.g., password-reset)
  - lib/ — Auth, env, logger, email service, utils
    - email/ — Multi-provider email service (Nodemailer, Resend)
  - emails/ — React Email templates (e.g., reset-password-email.tsx)
  - actions/ — Email sending actions (e.g., send-password-reset-email.tsx)
  - schemas/ — Zod validation schemas
  - tests/ — Test setup and tests (Bun test runner)
- examples/ — Usage examples (email-service-usage.ts)
- public/ — Static assets (e.g., web manifest)
- .env.example — Example environment configuration
- bunfig.toml — Bun test config
- eslint.config.ts, .prettierrc, .editorconfig — Linting/formatting rules
- package.json — Scripts and dependencies
- EMAIL_SERVICE.md — Email service documentation

### How to run

- Install dependencies:
  - bun install
- Copy env and configure:
  - cp .env.example .env
  - Set API_KEY and other values
- Development server (watch):
  - bun run dev
- Email template development:
  - bun run dev:emails (React Email preview server on port 3002)
- Production start:
  - bun run start

### Tests, linting, formatting

- Run tests:
  - bun test
  - bun test:watch
- Lint:
  - bun run lint:fix (auto-fix)
  - bun run lint
- Format:
  - bun run format (write)
  - bun run format:check
- Test configuration: bunfig.toml preloads `src/tests/setup.ts` (sets NODE_ENV=test, silent logs, default test API_KEY/PORT)

### API surface (current)

- Public:
  - GET / — Service status
  - GET /health — Health check
- Protected (require API key):
  - POST /emails/v1/password-reset — Send password reset email
- Authentication options for protected endpoints:
  - Authorization: Bearer <API_KEY>
  - x-api-key: <API_KEY>

### Environment variables

- NODE_ENV: development | production | test (default: production)
- PORT: number (default: 3001)
- LOG_LEVEL: fatal | error | warn | info | debug | trace | silent (default: info)
- API_KEY: required for protected routes

Email service configuration (optional):

- SMTP_HOST: SMTP server host (default: localhost)
- SMTP_PORT: SMTP server port (default: 587)
- SMTP_SECURE: Use secure connection (default: false)
- SMTP_USER: SMTP username
- SMTP_PASS: SMTP password
- DEFAULT_FROM_EMAIL: Default sender email address
- FRONTEND_URL: Frontend URL for email links

### Conventions & toolchain

- Module system: ESM ("type": "module")
- TS config: strict, bundler module resolution, path alias ~/_ → src/_
- Logging: Pino (respect LOG_LEVEL)
- Validation: Zod for env schema (fails fast with clear errors)
- Email: React Email components for templates, multi-provider service (Nodemailer, Resend)
- Linting: ESLint 9 (typescript-eslint); Prettier 3 with import-sort plugin
- Formatting: 120 char width, single quotes, trailing commas, sorted imports

### Operating rules for Augment Agent

1. Information gathering

- Prefer the smallest set of high-signal queries.
- Use codebase-retrieval for high-level “where/how” questions.
- Use view for specific files/lines; use search_query_regex for symbol lookups.
- Avoid broad scans and repeated searches without a clear next step.

2. Making edits

- Use str-replace-editor for modifications; do not recreate files wholesale.
- Confirm existence and signatures of functions/classes before editing.
- Make conservative, minimal changes; keep edits under 150 lines per step.
- Respect existing patterns, import order, and file organization.

3. Dependencies

- Use package managers; do not hand-edit package.json.
  - With Bun: bun add <pkg>, bun remove <pkg>
- Seek explicit permission before installing/upgrading dependencies.

4. Testing and validation

- When code changes are made, write/update tests as needed.
- Safe-by-default checks you may run without asking:
  - bun test
  - bun run lint:check
  - bun run format:check
- Consider success only with exit code 0 and clean logs.

5. Running commands

- Keep commands minimal and fast; summarize what was run and results.
- Start long-running servers only when explicitly requested.
- Never deploy, commit, push, or run destructive operations without permission.

6. Displaying code in chat

- When showing existing file snippets to the user, wrap them in:
  - <augment_code_snippet path="relative/path" mode="EXCERPT"> ... </augment_code_snippet>
- Keep snippets under 10 lines; the user can click through for full context.

7. Task management

- Use a tasklist when work is non-trivial or ambiguous (e.g., multi-file changes, planning requested, or >2 edit/verify iterations expected).
- Start with an Investigate/Triage task set to IN_PROGRESS, then iterate.
- Keep exactly one task IN_PROGRESS; batch state updates.

8. Security & privacy

- Treat API keys and env values as secrets; do not log or expose them.
- Be mindful of auth on protected endpoints; tests expect both Bearer and x-api-key behavior.

### Typical change workflow

1. Investigate:

- Identify the target file(s) and confirm function/class signatures.

2. Implement:

- Use str-replace-editor with small, focused diffs.
- Adhere to ESLint/Prettier rules; sort imports.

3. Validate:

- bun run lint:check && bun run format:check && bun test

4. Iterate:

- Address failures with minimal additional changes; re-run checks.

5. Ask:

- If blocked by missing requirements or risky steps (deps, migrations, deployments), ask the user before proceeding.

### Email service architecture

- Multi-provider email service with unified interface
- Providers: Nodemailer (SMTP), Resend (API-based, placeholder)
- Service registry for managing multiple email service instances
- React Email components for template rendering
- Email actions for business logic (e.g., password reset emails)
- Comprehensive error handling and logging
- See EMAIL_SERVICE.md for detailed documentation

### Extending the service (example guidance)

- Add a new v1 endpoint:
  - Create a controller under src/controllers/v1.
  - Wire it in the v1 router and protect it with apiKeyAuth where required.
  - Add unit/integration tests under src/tests (mirroring existing patterns).
  - Update README if public API changes.

- Add a new email template:
  - Create React Email component in src/emails/
  - Add corresponding action in src/actions/
  - Test with bun run dev:emails for preview
  - Add integration tests for the new email functionality

### Quick command reference

- Install: bun install
- Dev: bun run dev
- Email templates: bun run dev:emails
- Start: bun run start
- Test: bun test | bun test:watch
- Lint: bun run lint | bun run lint:check
- Format: bun run format | bun run format:check

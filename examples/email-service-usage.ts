import type { EmailOptions } from '~/lib/email';

import { EmailServiceFactory, getEmailRegistry, sendEmail } from '~/lib/email';

/**
 * Email Service Usage Examples
 *
 * This file demonstrates how to use the email service with different providers
 * and configurations.
 */

// Example 1: Simple email sending using the default service
async function sendSimpleEmail() {
  try {
    const result = await sendEmail({
      to: '<EMAIL>',
      from: '<EMAIL>',
      subject: 'Welcome to our service!',
      text: 'Thank you for signing up!',
      html: '<h1>Welcome!</h1><p>Thank you for signing up!</p>',
    });

    console.log('Email sent:', result);
  } catch (error) {
    console.error('Failed to send email:', error);
  }
}

// Example 2: Complex email with multiple recipients and attachments
async function sendComplexEmail() {
  const emailOptions: EmailOptions = {
    to: [{ email: '<EMAIL>', name: 'User One' }, '<EMAIL>'],
    from: { email: '<EMAIL>', name: 'Your App Team' },
    subject: 'Monthly Newsletter',
    html: `
      <h2>Monthly Newsletter</h2>
      <p>Here's what's new this month...</p>
      <ul>
        <li>Feature 1</li>
        <li>Feature 2</li>
        <li>Bug fixes</li>
      </ul>
    `,
    cc: '<EMAIL>',
    bcc: ['<EMAIL>'],
    replyTo: '<EMAIL>',
    attachments: [
      {
        filename: 'newsletter.pdf',
        content: Buffer.from('PDF content here'),
        contentType: 'application/pdf',
      },
    ],
    headers: {
      'X-Campaign-ID': 'newsletter-2024-01',
    },
  };

  try {
    const result = await sendEmail(emailOptions);
    console.log('Complex email sent:', result);
  } catch (error) {
    console.error('Failed to send complex email:', error);
  }
}

// Example 3: Using multiple email providers
function setupMultipleProviders() {
  const registry = getEmailRegistry();

  // Register Nodemailer for transactional emails
  registry.registerFromConfig('transactional', {
    provider: 'nodemailer',
    config: {
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: 'your-app-password',
      },
    },
  });

  // Register Resend for marketing emails (when implemented)
  registry.registerFromConfig('marketing', {
    provider: 'resend',
    config: {
      apiKey: 'your-resend-api-key',
    },
  });

  // Set transactional as default
  registry.setDefault('transactional');

  // Send using specific provider
  const transactionalService = registry.get('transactional');
  const marketingService = registry.get('marketing');

  console.log('Transactional provider:', transactionalService.getProvider());
  console.log('Marketing provider:', marketingService.getProvider());
}

// Example 4: Creating custom email service instance
async function createCustomService() {
  const customService = EmailServiceFactory.create({
    provider: 'nodemailer',
    config: {
      host: 'smtp.custom.com',
      port: 465,
      secure: true,
      auth: {
        user: '<EMAIL>',
        pass: 'custom-password',
      },
    },
  });

  // Test the connection
  if (customService.test) {
    const isConnected = await customService.test();
    console.log('Custom service connection test:', isConnected);
  }

  // Send email using custom service
  const result = await customService.send({
    to: '<EMAIL>',
    from: '<EMAIL>',
    subject: 'Custom Service Email',
    text: 'This email was sent using a custom service instance.',
  });

  console.log('Custom service result:', result);
}

// Example 5: Error handling and retry logic
async function sendEmailWithRetry(emailOptions: EmailOptions, maxRetries = 3) {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await sendEmail(emailOptions);

      if (result.success) {
        console.log(`Email sent successfully on attempt ${attempt}:`, result);
        return result;
      } else {
        console.warn(`Email sending failed on attempt ${attempt}:`, result.message);
        lastError = new Error(result.error || result.message);
      }
    } catch (error) {
      console.error(`Email sending error on attempt ${attempt}:`, error);
      lastError = error instanceof Error ? error : new Error(String(error));
    }

    if (attempt < maxRetries) {
      const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
      console.log(`Retrying in ${delay}ms...`);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw lastError || new Error('Failed to send email after all retries');
}

// Example usage
// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function runExamples() {
  console.log('=== Email Service Examples ===\n');

  console.log('1. Simple email:');
  await sendSimpleEmail();

  console.log('\n2. Complex email:');
  await sendComplexEmail();

  console.log('\n3. Multiple providers setup:');
  setupMultipleProviders();

  console.log('\n4. Custom service:');
  await createCustomService();

  console.log('\n5. Email with retry:');
  await sendEmailWithRetry({
    to: '<EMAIL>',
    from: '<EMAIL>',
    subject: 'Test with Retry',
    text: 'This email will be retried if it fails.',
  });
}

// Uncomment to run examples
// runExamples().catch(console.error);

# emails

Finanze.Pro Emails Service - A microservice for handling email functionality.

## Setup

To install dependencies:

```bash
bun install
```

Copy the environment configuration:

```bash
cp .env.example .env
```

Update the `.env` file with your configuration, especially the `API_KEY` for authentication.

## Running the Service

Development mode (with file watching):

```bash
bun run dev
```

Production mode:

```bash
bun run start
```

## Testing

Run all tests:

```bash
bun test
```

Run tests in watch mode:

```bash
bun test:watch
```

The test suite includes:

- **Unit tests** for authentication middleware
- **Integration tests** for API endpoints
- **Controller tests** for business logic
- **Environment configuration tests**

## Authentication

All endpoints under `/emails/*` are protected with API key authentication. You must provide a valid API key in one of the following ways:

### Authorization Header (Bearer Token)

```bash
curl -H "Authorization: Bearer your-api-key-here" http://localhost:3001/emails/v1/password-reset
```

### X-API-Key Header

```bash
curl -H "x-api-key: your-api-key-here" http://localhost:3001/emails/v1/password-reset
```

## API Endpoints

### Public Endpoints

- `GET /` - Service status
- `GET /health` - Health check

### Protected Endpoints (require API key)

- `POST /emails/v1/password-reset` - Send password reset email

## Environment Variables

- `NODE_ENV` - Environment (development, production, test)
- `PORT` - Server port (default: 3001)
- `LOG_LEVEL` - Logging level (default: info)
- `API_KEY` - Static API key for authentication (required)

[Bun](https://bun.sh) is a fast all-in-one JavaScript runtime.


## For Contributors

See AGENT.md for the agent's usage and development rules:

- AGENT guide: [AGENT.md](./AGENT.md)

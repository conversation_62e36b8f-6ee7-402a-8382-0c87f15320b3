import type { NextFunction, Request, Response } from 'express';

import env from './env';
import logger from './logger';

/**
 * Middleware to authenticate requests using a static API key
 * Expects the API key to be provided in the Authorization header as "Bearer <api_key>"
 * or in the x-api-key header
 */
export const apiKeyAuth = (req: Request, res: Response, next: NextFunction): void => {
  const authHeader = req.headers.authorization;
  const apiKeyHeader = req.headers['x-api-key'] as string;

  let providedApiKey: string | undefined;

  // Check Authorization header (Bearer token format)
  if (authHeader && authHeader.startsWith('Bearer ')) {
    providedApiKey = authHeader.substring(7); // Remove "Bearer " prefix
  }
  // Check x-api-key header
  else if (apiKeyHeader) {
    providedApiKey = apiKeyHeader;
  }

  if (!providedApiKey) {
    logger.warn({ ip: req.ip, path: req.path }, 'API key authentication failed: No API key provided');
    res.status(401).json({
      success: false,
      error: 'Authentication required',
      message: 'API key must be provided in Authorization header (Bearer token) or x-api-key header',
    });
    return;
  }

  if (providedApiKey !== env.API_KEY) {
    logger.warn({ ip: req.ip, path: req.path }, 'API key authentication failed: Invalid API key');
    res.status(401).json({
      success: false,
      error: 'Authentication failed',
      message: 'Invalid API key',
    });
    return;
  }

  // Authentication successful
  logger.debug({ ip: req.ip, path: req.path }, 'API key authentication successful');
  next();
};

import type { EmailOptions, EmailResult, EmailService } from '../types';

export interface ResendConfig {
  apiKey: string;
  baseUrl?: string;
}

/**
 * Resend email service implementation (placeholder)
 * TODO: Implement when Resend package is added
 */
export class ResendEmailService implements EmailService {
  private config: ResendConfig;

  constructor(config: ResendConfig) {
    this.config = config;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/require-await
  async send(options: EmailOptions): Promise<EmailResult> {
    const timestamp = new Date();

    // TODO: Implement Resend API integration
    // For now, return a placeholder response
    return {
      success: false,
      message: 'Resend provider not yet implemented',
      error: 'Provider implementation pending',
      provider: 'resend',
      timestamp,
    };
  }

  getProvider(): string {
    return 'resend';
  }

  // eslint-disable-next-line @typescript-eslint/require-await
  async test(): Promise<boolean> {
    // TODO: Implement Resend connection test
    return false;
  }
}

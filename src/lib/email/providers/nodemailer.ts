import type { Transporter } from 'nodemailer';
import type { SentMessageInfo } from 'nodemailer/lib/smtp-transport';
import type { EmailAddress, EmailOptions, EmailResult, EmailService } from '../types';

import nodemailer from 'nodemailer';

export interface NodemailerConfig {
  host: string;
  port: number;
  secure?: boolean;
  auth?: {
    user: string;
    pass: string;
  };
  tls?: {
    rejectUnauthorized?: boolean;
  };
}

/**
 * Nodemailer email service implementation
 */
export class NodemailerEmailService implements EmailService {
  private transporter: Transporter;

  constructor(config: NodemailerConfig) {
    this.transporter = nodemailer.createTransport(config);
  }

  async send(options: EmailOptions): Promise<EmailResult> {
    const timestamp = new Date();

    try {
      // Convert EmailAddress objects to strings for nodemailer
      const formatAddress = (value: EmailAddress | string): string => {
        if (typeof value === 'string') return value;
        return value.name ? `"${value.name}" <${value.email}>` : value.email;
      };

      const formatAddresses = (
        value: EmailAddress | EmailAddress[] | string | string[] | Array<EmailAddress | string>
      ): string => {
        if (Array.isArray(value)) {
          return value.map(formatAddress).join(', ');
        }
        return formatAddress(value);
      };

      const mailOptions = {
        from: formatAddress(options.from),
        to: formatAddresses(options.to),
        subject: options.subject,
        text: options.text,
        html: options.html,
        cc: options.cc ? formatAddresses(options.cc) : undefined,
        bcc: options.bcc ? formatAddresses(options.bcc) : undefined,
        replyTo: options.replyTo ? formatAddress(options.replyTo) : undefined,
        attachments: options.attachments?.map((att) => ({
          filename: att.filename,
          content: att.content,
          contentType: att.contentType,
          encoding: att.encoding,
        })),
        headers: options.headers,
      };

      const info = (await this.transporter.sendMail(mailOptions)) as SentMessageInfo;

      return {
        success: true,
        messageId: info.messageId,
        message: 'Email sent successfully',
        provider: 'nodemailer',
        timestamp,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      return {
        success: false,
        message: 'Failed to send email',
        error: errorMessage,
        provider: 'nodemailer',
        timestamp,
      };
    }
  }

  getProvider(): string {
    return 'nodemailer';
  }

  async test(): Promise<boolean> {
    try {
      await this.transporter.verify();
      return true;
    } catch {
      return false;
    }
  }
}

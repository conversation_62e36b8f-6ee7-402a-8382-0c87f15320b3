/**
 * Email service types and interfaces
 */

export interface EmailAddress {
  email: string;
  name?: string;
}

export interface EmailAttachment {
  filename: string;
  content: string | Buffer;
  contentType?: string;
  encoding?: string;
}

export interface EmailOptions {
  to: EmailAddress | EmailAddress[] | string | string[] | Array<EmailAddress | string>;
  from: EmailAddress | string;
  subject: string;
  text?: string;
  html?: string;
  cc?: EmailAddress | EmailAddress[] | string | string[];
  bcc?: EmailAddress | EmailAddress[] | string | string[];
  replyTo?: EmailAddress | string;
  attachments?: EmailAttachment[];
  headers?: Record<string, string>;
}

export interface EmailResult {
  success: boolean;
  messageId?: string;
  message: string;
  error?: string;
  provider: string;
  timestamp: Date;
}

export interface EmailServiceConfig {
  provider: 'nodemailer' | 'resend';
  config: Record<string, unknown>;
}

/**
 * Email service interface that all providers must implement
 */
export interface EmailService {
  /**
   * Send an email
   */
  send(options: EmailOptions): Promise<EmailResult>;

  /**
   * Get the provider name
   */
  getProvider(): string;

  /**
   * Test the connection/configuration
   */
  test?(): Promise<boolean>;
}

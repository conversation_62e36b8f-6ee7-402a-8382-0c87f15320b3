import type { NodemailerConfig } from './providers/nodemailer';
import type { ResendConfig } from './providers/resend';
import type { EmailService, EmailServiceConfig } from './types';

import { NodemailerEmailService } from './providers/nodemailer';
import { ResendEmailService } from './providers/resend';

/**
 * Email service factory for creating email service instances
 */
export class EmailServiceFactory {
  /**
   * Create an email service instance based on configuration
   */
  static create(config: EmailServiceConfig): EmailService {
    switch (config.provider) {
      case 'nodemailer':
        return new NodemailerEmailService(config.config as unknown as NodemailerConfig);

      case 'resend':
        return new ResendEmailService(config.config as unknown as ResendConfig);

      default:
        throw new Error(`Unsupported email provider: ${config.provider as string}`);
    }
  }

  /**
   * Get list of supported providers
   */
  static getSupportedProviders(): string[] {
    return ['nodemailer', 'resend'];
  }
}

/**
 * Email service registry for managing multiple service instances
 */
export class EmailServiceRegistry {
  private services = new Map<string, EmailService>();
  private defaultService?: string;

  /**
   * Register an email service
   */
  register(name: string, service: EmailService): void {
    this.services.set(name, service);

    // Set as default if it's the first service
    if (!this.defaultService) {
      this.defaultService = name;
    }
  }

  /**
   * Register an email service from configuration
   */
  registerFromConfig(name: string, config: EmailServiceConfig): void {
    const service = EmailServiceFactory.create(config);
    this.register(name, service);
  }

  /**
   * Get an email service by name
   */
  get(name?: string): EmailService {
    const serviceName = name || this.defaultService;

    if (!serviceName) {
      throw new Error('No email service specified and no default service set');
    }

    const service = this.services.get(serviceName);
    if (!service) {
      throw new Error(`Email service '${serviceName}' not found`);
    }

    return service;
  }

  /**
   * Set the default service
   */
  setDefault(name: string): void {
    if (!this.services.has(name)) {
      throw new Error(`Cannot set default: service '${name}' not found`);
    }
    this.defaultService = name;
  }

  /**
   * Get all registered service names
   */
  getServiceNames(): string[] {
    return Array.from(this.services.keys());
  }

  /**
   * Check if a service is registered
   */
  has(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Remove a service
   */
  unregister(name: string): boolean {
    const removed = this.services.delete(name);

    // Reset default if we removed the default service
    if (this.defaultService === name) {
      this.defaultService = this.services.size > 0 ? this.services.keys().next().value : undefined;
    }

    return removed;
  }

  /**
   * Clear all services
   */
  clear(): void {
    this.services.clear();
    this.defaultService = undefined;
  }
}

import type { Request, Response } from 'express';

import { z } from 'zod';

import { sendPasswordResetEmail } from '~/actions/send-password-reset-email';
import logger from '~/lib/logger';
import { PasswordResetSchema } from '~/schemas/password-reset';

const passwordReset = async (req: Request, res: Response) => {
  try {
    // Validate request body
    const validationResult = PasswordResetSchema.safeParse(req.body);

    if (!validationResult.success) {
      logger.warn(
        {
          errors: z.treeifyError(validationResult.error),
          body: req.body as unknown as object,
        },
        'Invalid password reset request'
      );

      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        message: 'Invalid email address provided',
        details: z.treeifyError(validationResult.error),
      });
    }

    const result = await sendPasswordResetEmail(validationResult.data);

    if (result.success) {
      return res.status(200).json({
        success: true,
        message: 'Password reset email sent successfully',
        messageId: result.messageId,
      });
    } else {
      return res.status(500).json({
        success: false,
        error: 'Email sending failed',
        message: result.message,
      });
    }
  } catch (error) {
    logger.error({ error: (error as Error).message }, 'Unexpected error in password reset controller');

    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'An unexpected error occurred while sending password reset email',
    });
  }
};

export default passwordReset;

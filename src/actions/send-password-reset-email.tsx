import type { PasswordResetRequest } from '~/schemas/password-reset';

import { render } from '@react-email/components';

import ResetPasswordEmail from '~/emails/reset-password-email';
import { sendEmail } from '~/lib/email';

export async function sendPasswordResetEmail(params: PasswordResetRequest) {
  const { email, resetUrl } = params;

  const htmlContent = await render(<ResetPasswordEmail name={params.name} resetUrl={resetUrl} />);

  // Send password reset email
  return await sendEmail({
    to: email,
    from: process.env.DEFAULT_FROM_EMAIL || '<EMAIL>',
    subject: '[Finanze.Pro] Password Reset Request',
    text: `You have requested a password reset. Please visit the following link to reset your password: ${resetUrl}`,
    html: htmlContent,
  });
}

import { Body, Container, Head, Html, Img, Preview, Section, Text } from '@react-email/components';

import * as styles from './styles';

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <Html>
      <Head />
      <Body style={styles.main}>
        <Preview>Finanze.Pro Password Reset</Preview>
        <Container style={styles.container}>
          <Section style={styles.header}>
            <Img src="https://finanze.pro/logo.webp" alt="Finanze.Pro Logo" width={44} height={44} />
          </Section>

          {children}

          <Section>
            <Text style={styles.text}>Finanze.Pro Team.</Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

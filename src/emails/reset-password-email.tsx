import { Button, Section, Text } from '@react-email/components';

import Layout from './shared/layout';
import * as styles from './shared/styles';

interface Props {
  name: string;
  resetUrl: string;
}

export default function ResetPasswordEmail({ name, resetUrl }: Props) {
  return (
    <Layout>
      <Section>
        <Text style={styles.text}>Hi {name},</Text>
        <Text style={styles.text}>
          Someone recently requested a password change for your Finanze.Pro account. If this was you, you can set a new
          password here:
        </Text>
        <Button style={styles.button} href={resetUrl}>
          Reset password
        </Button>
        <Text style={styles.text}>
          If you don&apos;t want to change your password or didn&apos;t request this, just ignore and delete this
          message.
        </Text>
        <Text style={styles.text}>To keep your account secure, please don&apos;t forward this email to anyone.</Text>
      </Section>
    </Layout>
  );
}

ResetPasswordEmail.PreviewProps = {
  name: '<PERSON>',
  resetUrl: 'https://finanze.pro/reset-password',
};

import { describe, expect, test } from 'bun:test';

describe('Password Reset Controller', () => {
  test('should be importable', async () => {
    const passwordReset = await import('~/controllers/v1/password-reset');
    expect(typeof passwordReset.default).toBe('function');
  });

  test('should have correct function signature', async () => {
    const passwordReset = await import('~/controllers/v1/password-reset');
    expect(passwordReset.default.length).toBe(2); // req, res parameters
  });
});

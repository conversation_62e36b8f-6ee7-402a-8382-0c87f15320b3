import { describe, expect, test } from 'bun:test';

describe('Authentication middleware', () => {
  test('should be importable', async () => {
    const { apiKeyAuth } = await import('~/lib/auth');
    expect(typeof apiKeyAuth).toBe('function');
  });

  test('should have correct function signature', async () => {
    const { apiKeyAuth } = await import('~/lib/auth');
    expect(apiKeyAuth.length).toBe(3); // req, res, next parameters
  });
});

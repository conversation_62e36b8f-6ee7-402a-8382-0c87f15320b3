import type { EmailServiceConfig } from '~/lib/email/types';

import { beforeEach, describe, expect, test } from 'bun:test';

import { EmailServiceFactory, EmailServiceRegistry } from '~/lib/email/factory';
import { NodemailerEmailService } from '~/lib/email/providers/nodemailer';
import { ResendEmailService } from '~/lib/email/providers/resend';

describe('Email Service', () => {
  describe('EmailServiceFactory', () => {
    test('should create NodemailerEmailService', () => {
      const config: EmailServiceConfig = {
        provider: 'nodemailer',
        config: {
          host: 'smtp.example.com',
          port: 587,
          auth: {
            user: '<EMAIL>',
            pass: 'password',
          },
        },
      };

      const service = EmailServiceFactory.create(config);
      expect(service).toBeInstanceOf(NodemailerEmailService);
      expect(service.getProvider()).toBe('nodemailer');
    });

    test('should create ResendEmailService', () => {
      const config: EmailServiceConfig = {
        provider: 'resend',
        config: {
          apiKey: 'test-api-key',
        },
      };

      const service = EmailServiceFactory.create(config);
      expect(service).toBeInstanceOf(ResendEmailService);
      expect(service.getProvider()).toBe('resend');
    });

    test('should throw error for unsupported provider', () => {
      const config = {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-assignment
        provider: 'unsupported' as any,
        config: {},
      };

      expect(() => EmailServiceFactory.create(config)).toThrow('Unsupported email provider: unsupported');
    });

    test('should return supported providers', () => {
      const providers = EmailServiceFactory.getSupportedProviders();
      expect(providers).toEqual(['nodemailer', 'resend']);
    });
  });

  describe('EmailServiceRegistry', () => {
    let registry: EmailServiceRegistry;

    beforeEach(() => {
      registry = new EmailServiceRegistry();
    });

    test('should register and retrieve services', () => {
      const config: EmailServiceConfig = {
        provider: 'nodemailer',
        config: {
          host: 'smtp.example.com',
          port: 587,
        },
      };

      registry.registerFromConfig('test', config);

      const service = registry.get('test');
      expect(service).toBeInstanceOf(NodemailerEmailService);
      expect(service.getProvider()).toBe('nodemailer');
    });

    test('should set first service as default', () => {
      const config: EmailServiceConfig = {
        provider: 'nodemailer',
        config: {
          host: 'smtp.example.com',
          port: 587,
        },
      };

      registry.registerFromConfig('first', config);

      const service = registry.get(); // No name provided, should get default
      expect(service).toBeInstanceOf(NodemailerEmailService);
    });

    test('should throw error when no service found', () => {
      expect(() => registry.get('nonexistent')).toThrow("Email service 'nonexistent' not found");
    });

    test('should throw error when no default service set', () => {
      expect(() => registry.get()).toThrow('No email service specified and no default service set');
    });

    test('should manage service names', () => {
      const config: EmailServiceConfig = {
        provider: 'nodemailer',
        config: { host: 'smtp.example.com', port: 587 },
      };

      registry.registerFromConfig('service1', config);
      registry.registerFromConfig('service2', config);

      expect(registry.getServiceNames()).toEqual(['service1', 'service2']);
      expect(registry.has('service1')).toBe(true);
      expect(registry.has('nonexistent')).toBe(false);
    });

    test('should unregister services', () => {
      const config: EmailServiceConfig = {
        provider: 'nodemailer',
        config: { host: 'smtp.example.com', port: 587 },
      };

      registry.registerFromConfig('test', config);
      expect(registry.has('test')).toBe(true);

      const removed = registry.unregister('test');
      expect(removed).toBe(true);
      expect(registry.has('test')).toBe(false);
    });

    test('should clear all services', () => {
      const config: EmailServiceConfig = {
        provider: 'nodemailer',
        config: { host: 'smtp.example.com', port: 587 },
      };

      registry.registerFromConfig('service1', config);
      registry.registerFromConfig('service2', config);

      expect(registry.getServiceNames()).toHaveLength(2);

      registry.clear();
      expect(registry.getServiceNames()).toHaveLength(0);
    });
  });
});

import express from 'express';
import { pinoHttp } from 'pino-http';

import v1Router from './controllers/v1';
import { apiKeyAuth } from './lib/auth';
import { initializeEmailServices } from './lib/email';
import env from './lib/env';
import logger from './lib/logger';

const app = express();

initializeEmailServices();

app.use(pinoHttp({ logger }));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static('public'));

app.get('/', (_req, res) => {
  res.json({ message: 'OK', success: true, app: 'Finanze.Pro Emails Service' });
});

app.get('/health', (_req, res) => {
  res.json({ message: 'Healthy', success: true });
});

app.use('/emails/v1', apiKeyAuth, v1Router);

app.listen(env.PORT, () => {
  console.log(`Server is running at http://localhost:${env.PORT}`);
  console.log(`Environment: ${env.NODE_ENV}`);
});
